import psycopg2
import jinja2
import pypandoc
import os

# === 1. Fetch mission and image from DB ===
conn = psycopg2.connect(
    dbname="jedsy",
    user="postgres",
    password="StrongSecurePassword123",
    host="localhost",
    port="5432"
)
cur = conn.cursor()

cur.execute("SELECT id, mission_code, title, revision, date, description FROM missions WHERE mission_code = '024' ORDER BY id DESC LIMIT 1")
mission = cur.fetchone()

cur.execute("SELECT path FROM images WHERE mission_id = %s AND type = 'overview' ORDER BY id DESC LIMIT 1", (mission[0],))
image = cur.fetchone()

cur.close()
conn.close()

data = {
    "mission_code": mission[1],
    "title": mission[2],
    "revision": mission[3],
    "date": mission[4].strftime("%Y-%m-%d"),
    "description": mission[5],
    "image_path": image[0].replace("\\", "/")  # Pandoc prefers forward slashes
}

# === 2. Load Jinja2 template ===
with open("template.md") as f:
    template = jinja2.Template(f.read())

rendered_md = template.render(**data)

# === 3. Write rendered Markdown to file ===
md_filename = f"output/OM_{data['mission_code']}_{data['revision']}.md"
pdf_filename = md_filename.replace(".md", ".pdf")

os.makedirs("output", exist_ok=True)

with open(md_filename, "w") as f:
    f.write(rendered_md)

# === 4. Convert to PDF using Pandoc ===
pypandoc.convert_text(
    rendered_md,
    'pdf',
    format='md',
    outputfile=pdf_filename,
    extra_args=[
        '--standalone',
        '--pdf-engine=xelatex',
        '--template=jedsy_template.tex',
        f'--variable=revision:{data["revision"]}',
        f'--variable=revision:{data["title"]}'
    ]
)

print(f"✅ PDF report generated: {pdf_filename}")
