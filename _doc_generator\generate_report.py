import psycopg2
import jinja2
import pypandoc
import os
import subprocess

# === 1. Fetch mission and image from DB ===
conn = psycopg2.connect(
    dbname="jedsy",
    user="postgres",
    password="StrongSecurePassword123",
    host="localhost",
    port="5432"
)
cur = conn.cursor()

cur.execute("""
    SELECT id, mission_code, title, revision, date, description 
    FROM missions 
    WHERE mission_code = '024' 
    ORDER BY id DESC LIMIT 1
""")
mission = cur.fetchone()

cur.execute("""
    SELECT path 
    FROM images 
    WHERE mission_id = %s AND type = 'overview' 
    ORDER BY id DESC LIMIT 1
""", (mission[0],))
image = cur.fetchone()

cur.close()
conn.close()

data = {
    "mission_code": mission[1],
    "title": "Jedsy Operation Manual",
    "route_id": mission[2],
    #"revision": mission[3],
    "revision": f"{int(mission[3]):02}",
    "date": mission[4].strftime("%Y-%m-%d"),
    "description": mission[5],
    "image_path": image[0].replace("\\", "/")  # Pandoc prefers forward slashes
}

# === 2. Load Jinja2 template ===
with open("template.md") as f:
    template = jinja2.Template(f.read())

rendered_md = template.render(**data)

# === 3. Write rendered Markdown to file ===
os.makedirs("output", exist_ok=True)

md_filename = f"output/OM_{data['mission_code']}_{data['revision']}.md"
pdf_filename = md_filename.replace(".md", ".pdf")

with open(md_filename, "w", encoding="utf-8") as f:
    f.write(rendered_md)

# === 4. Convert to PDF using Pandoc ===
# Use subprocess so we can get verbose output
cmd = [
    "pandoc",
    md_filename,
    "--from=markdown",
    "--to=pdf",
    "--output", pdf_filename,
    "--standalone",
    "--pdf-engine=xelatex",
    "--template=jedsy_template.tex",
    f"--metadata=title:{data['title']}",
    f"--metadata=subtitle:{data['route_id']}",
    f"--metadata=route_id:{data['route_id']}",
    f"--metadata=revision:{data['revision']}"
    #"--verbose"
]

print("ℹ️  Running Pandoc with command:")
print(" ".join(cmd))

subprocess.run(cmd, check=True)

print(f"✅ PDF report generated: {pdf_filename}")

# Open the PDF file
import os
import platform

def open_pdf(file_path):
    """Open PDF file with the default system application"""
    try:
        # Convert to absolute path to ensure it's found
        abs_path = os.path.abspath(file_path)
        print(f"📖 Attempting to open PDF: {abs_path}")

        if not os.path.exists(abs_path):
            print(f"❌ PDF file not found: {abs_path}")
            return

        if platform.system() == 'Windows':
            os.startfile(abs_path)
        elif platform.system() == 'Darwin':  # macOS
            subprocess.run(['open', abs_path])
        else:  # Linux
            subprocess.run(['xdg-open', abs_path])
        print(f"✅ PDF opened successfully")
    except Exception as e:
        print(f"❌ Could not open PDF: {e}")

open_pdf(pdf_filename)