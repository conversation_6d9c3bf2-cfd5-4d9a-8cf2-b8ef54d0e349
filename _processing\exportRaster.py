from qgis.PyQt.QtCore import QCoreApplication
from PyQt5.QtCore import QSize, QEventLoop
from qgis.core import (
    QgsProcessing,
    QgsProcessingException,
    QgsProcessingAlgorithm,
    QgsProcessingParameterVectorLayer,
    QgsProcessingParameterEnum,
    QgsProcessingParameterString,
    QgsProcessingParameterFolderDestination,
    QgsProcessingParameterBoolean,
    QgsProject,
    QgsMapRendererParallelJob,
    QgsVectorLayer,
    QgsExpression,
    QgsFeatureRequest,
    QgsRectangle,
    QgsMapLayer,
    QgsCoordinateTransform,
    NULL
)

from qgis.PyQt.QtGui import QColor

from qgis import processing
import os
from qgis.utils import iface
import re
import importlib.util
import sys
import time

try:
    module_path = os.path.join(os.path.dirname(__file__), 'common.py')
except:
    module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_common','_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)

class exportRaster(QgsProcessingAlgorithm):
    INPUT = 'INPUT'
    DEST = 'DEST'
    DEBUG = 'DEBUG'
    OVERWRITE = 'OVERWRITE'
    COUNTRY = 'COUNTRY'
    REVISION = 'REVISION'
    MAPTYPES = 'MAPTYPES'

    countries = ['Germany', 'Switzerland', 'Austria', 'Other']
    export_options = [
        "overview",
        "airRisk",
        "icao",
        "population",
        "Map (details)",
        "elevation",
        "MobileA",
        "MobileB",
        "MobileC",
        "MobileD",
        "hirf",
        "hirf_wr",
        "targets_a",
        "targets_b"
    ]

    message = (
        "Exports the following raster maps (depending on the operation size, this can take a long time):\n\n"
        "• overview\n"
        "• airRisk\n"
        "• icao\n"
        "• population (TIF needs to be converted to JPG)\n"
        "• Map (details) (TIF needs to be converted to JPG)\n"
        "• elevation\n\n"
        "• Mobile Network (Germany)\n\n"
        "• HIRF\n\n"
        "Note: Mobile network maps for countries other than Germany must still be exported manually.\n\n"
        "ver01"
    )

    def tr(self, string):
        return QCoreApplication.translate('Processing', string)

    def createInstance(self):
        return exportRaster()

    def name(self):
        return 'exportRaster'

    def displayName(self):
        return self.tr('Export Raster Maps')

    def group(self):
        return self.tr('Jedsy scripts')

    def groupId(self):
        return 'jedsyscripts'

    def shortHelpString(self):
        return self.tr(self.message)

    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading

    def __init__(self):
        super().__init__()
        self.cancel_requested = False
        self.current_render = None

    def initAlgorithm(self, config=None):
        self.addParameter(QgsProcessingParameterVectorLayer(self.INPUT, 'Operation layer'))
        self.addParameter(QgsProcessingParameterEnum(self.COUNTRY, 'Country', options=self.countries, defaultValue=0))
        self.addParameter(QgsProcessingParameterString(self.REVISION, 'Revision number', defaultValue='00'))
        default_path = os.path.dirname(QgsProject.instance().fileName()) if iface.activeLayer() == NULL else \
            os.path.split(iface.activeLayer().dataProvider().dataSourceUri())[0] + '/RASTER'
        self.addParameter(QgsProcessingParameterFolderDestination(self.DEST, 'Destination folder', defaultValue=default_path))
        self.addParameter(QgsProcessingParameterBoolean(self.OVERWRITE, 'Overwrite', defaultValue=True))
        self.addParameter(QgsProcessingParameterBoolean(self.DEBUG, 'Debug mode', defaultValue=False))
        self.addParameter(QgsProcessingParameterEnum(self.MAPTYPES, 'Maps to export',
                                                     options=self.export_options,
                                                     defaultValue=list(range(len(self.export_options))),
                                                     allowMultiple=True))

    def cancel(self):
        self.cancel_requested = True
        if self.current_render and self.current_render.isRunning():
            self.current_render.cancel()
            print("Render cancelled by user.")

    def render_map(self, output_folder, output_filename, output_format, res_dpi, px_per_unit, revision,
                   overwrite, extent, width_extent, height_extent, debug, opnum):
        if self.cancel_requested:
            print("Cancellation requested. Skipping render.")
            return
        
        # Define raw pixel dimensions
        raw_width_px = width_extent * px_per_unit
        raw_height_px = height_extent * px_per_unit
        min_px = 2000
        max_px = 28000

        # Determine scale factor
        max_dim = max(raw_width_px, raw_height_px)

        if max_dim > max_px:
            scale = max_px / max_dim  # scale down
        elif max_dim < min_px:
            scale = min_px / max_dim   # scale up
        else:
            scale = 1.0  # no scaling

        # Apply proportional scaling
        width_px = int(raw_width_px * scale)
        height_px = int(raw_height_px * scale)
      
        # output filename
        filename = f"{opnum}_{output_filename}_{revision}.{output_format}"
        output_path = os.path.join(output_folder, filename)

        if not overwrite and os.path.exists(output_path):
            print(f"File exists and overwrite is False: {output_path}")
            return

        if debug:
            print(f"DEBUG: Rendering: {output_path}")
            print(f"DEBUG: Size: {width_px} x {height_px}")
            print(f"DEBUG: Extent: {extent}")
            print(f"DEBUG: DPI: {res_dpi}")

        
        settings = iface.mapCanvas().mapSettings()
        settings.setExtent(extent)
        settings.setOutputSize(QSize(width_px, height_px))
        settings.setOutputDpi(res_dpi)

        visible_layers = QgsProject.instance().layerTreeRoot().checkedLayers()
        if debug:
            print(f"DEBUG: Found {len(visible_layers)} visible layers:")
            for layer in visible_layers:
                print(f"  - {layer.name()} (type: {layer.type()}, valid: {layer.isValid()})")

        if not visible_layers:
            print("⛔ ERROR: No visible layers found.")
            return

        settings.setLayers(visible_layers)
        '''
        # Force raster layers to refresh
        for layer in visible_layers:
            if layer.type() == QgsMapLayer.RasterLayer:
                layer.triggerRepaint()
                layer.dataProvider().reloadData()

        # Force canvas update
        iface.mapCanvas().refresh()
        QCoreApplication.processEvents()
        '''
        self.current_render = QgsMapRendererParallelJob(settings)
        
        render = self.current_render
        loop = QEventLoop()

        def finished():
            if self.cancel_requested:
                print("Render finished after cancellation.")
            else:
                img = render.renderedImage()
                if debug:
                    print(f"DEBUG: Rendered image size: {img.width()}x{img.height()}")
                    print(f"DEBUG: Image format: {img.format()}")
                    print(f"DEBUG: Image is null: {img.isNull()}")

                    # Check if image is all white/empty
                    if not img.isNull():
                        # Sample a few pixels to check if image is blank
                        center_pixel = img.pixel(img.width()//2, img.height()//2)
                        corner_pixel = img.pixel(10, 10)
                        print(f"DEBUG: Center pixel color: {hex(center_pixel)}")
                        print(f"DEBUG: Corner pixel color: {hex(corner_pixel)}")

                    #r, g, b, a = QColor(img.pixel(100, 100)).getRgb()
                    #print(f"DEBUG: Sample pixel RGB at (100,100): R={r}, G={g}, B={b}, A={a}")

                if img.save(output_path, output_format):
                    if debug:
                        print(f"Rendering complete: {output_path}")
                else:
                    print(f"⛔ ERROR: Failed to save image to {output_path}")
            loop.quit()

        render.finished.connect(finished)
        render.start()
        loop.exec_()

    def get_extents(self, layer,debug= False):
        canvas = iface.mapCanvas()
        canvas_crs = canvas.mapSettings().destinationCrs()
        bounding_crs = layer.crs()
        extent = layer.extent()

        if bounding_crs != canvas_crs:
            feedback.reportWarning(f"⚠ WARNING: {layer} has the wrong CRS: {bounding_crs}")
            if debug: print(f"Transforming extents because canvas_crs {canvas_crs} != bounding_crs {bounding_crs}")
            transform = QgsCoordinateTransform(bounding_crs, canvas_crs, QgsProject.instance())
            extent = transform.transformBoundingBox(extent)

        if debug:
            center = extent.center()
            print(f"DEBUG: Center coordinates: {center.x()}, {center.y()}")

        return extent


    def processAlgorithm(self, parameters, context, feedback):
        
        opnum, layerInput = common.find_op(self.parameterAsLayer(parameters, self.INPUT, context))
        country_id = parameters[self.COUNTRY]
        debug = parameters[self.DEBUG]
        output_folder = self.parameterAsString(parameters, self.DEST, context)
        revision = self.parameterAsString(parameters, self.REVISION, context)
        overwrite = parameters[self.OVERWRITE]
        selected_maps = parameters[self.MAPTYPES]
        
        layer_clipper = common.find_layer_by_name(f"{opnum}_clipper")
        layer_airrisk = common.find_layer_by_name(f"{opnum}_AirRisk")
        layer_targets = common.find_layer_by_name(f"{opnum}_Targets")

        # Check if required layers exist
        missing_layers = []
        if not layer_clipper:
            missing_layers.append(f"{opnum}_clipper")
        if not layer_airrisk:
            missing_layers.append(f"{opnum}_AirRisk")
        if not layer_targets:
            missing_layers.append(f"{opnum}_Targets")

        if missing_layers:
            msg = f"⛔ ERROR: Required layers not found: {', '.join(missing_layers)}"
            print(msg)
            raise QgsProcessingException(msg)

        # Check if layers are valid
        invalid_layers = []
        if not layer_clipper.isValid():
            invalid_layers.append(f"{opnum}_clipper")
        if not layer_airrisk.isValid():
            invalid_layers.append(f"{opnum}_AirRisk")
        if not layer_targets.isValid():
            invalid_layers.append(f"{opnum}_Targets")

        if invalid_layers:
            msg = f"⛔ ERROR: Required layers are invalid: {', '.join(invalid_layers)}"
            print(msg)
            raise QgsProcessingException(msg)

        # Check if layer CRS correspond to canvas CRS
        canvas_crs = iface.mapCanvas().mapSettings().destinationCrs()
        for layer in [layer_clipper, layer_airrisk, layer_targets]:
            if layer.crs() != canvas_crs:
                msg = f"⚠ WARNING: {layer.name()} CRS ({layer.crs().authid()}) does not match canvas CRS ({canvas_crs.authid()})"
                print(msg)
                feedback.pushWarning(msg)

        if debug:
            print(f"DEBUG: Operation number: {opnum}")
            print(f"DEBUG: Canvas CRS: {canvas_crs.authid()}")
            print(f"DEBUG: All required layers found and valid:")
            print(f"  - Clipper layer: {layer_clipper.name()} (CRS: {layer_clipper.crs().authid()})")
            print(f"  - AirRisk layer: {layer_airrisk.name()} (CRS: {layer_airrisk.crs().authid()})")
            print(f"  - Targets layer: {layer_targets.name()} (CRS: {layer_targets.crs().authid()})")
        


        #####################################
        ############## CONFIG ###############
        #####################################

        map_types_names = [
            'Initial Assessment',
            'Overview',
            'Air Risk',
            'ICAO',
            'Population',
            'Details',
            'Elevation',
            'Mobile A',
            'Mobile B',
            'Mobile C',
            'Mobile D',
            'HIRF',
            'Targets A',
            'Targets B'
        ]        

        # filename, fmt, px_per_unit, map_type name (of export maps script), fixed zoom (optional)
        all_parameter_sets = [
            ("overview", "jpg", 0.02, 'Overview'),
            ("airRisk", "jpg", 0.02, 'Air Risk'),
            ("icao", "jpg", 0.02, 'ICAO'),
            ("population", "tif", 0.08, 'Population'),
            ("Map", "tif", 0.08, 'Details'),
            ("elevation", "jpg", 0.04, 'Elevation'),
            ("MobileA", "jpg", 0.04, 'Mobile A'),
            ("MobileB", "jpg", 0.04, 'Mobile B'),
            ("MobileC", "jpg", 0.04, 'Mobile C'),
            ("MobileD", "jpg", 0.04, 'Mobile D'),
            ("hirf", "jpg", 0.8, 'HIRF', 6000),
            ("hirf_wr", "jpg", 0.02, 'HIRF'),
            ("targets_a", "jpg", 0.8, 'Targets A', 8000),
            ("targets_b", "jpg", 0.8, 'Targets B', 1000)
        ]        


        # target ratio for maps with fixed zoom
        target_ratio = 16 / 9

        if not os.path.exists(output_folder):
            os.mkdir(output_folder)

        for i in selected_maps:
            if self.cancel_requested:
                print("Cancelling remaining renders.")
                break
            
            # Unpack with default zoom_scale
            base_params = all_parameter_sets[i]
            if debug: print(f"processing {base_params}")
            # filename, fmt, px_per_unit, map_type = base_params[:4]
            filename, fmt, px_per_unit, map_type_key = base_params[:4]
            zoom_scale = base_params[4] if len(base_params) > 4 else 5000

         
            
            # determine canvas zoom
            if filename == "overview" or filename == "airRisk" or filename == "icao" or filename == "hirf_wr":
                #extent = layer_airrisk.extent()
                extent = self.get_extents(layer_airrisk,debug)
                extent.grow(extent.width() * 0.15)  # Grow by 15% of width (applies to all sides)
                if debug:
                    print(f"DEBUG: Using {layer_airrisk} extent for '{filename}': {extent}")
            else:
                #extent = layer_clipper.extent()
                extent = self.get_extents(layer_clipper,debug)
                if debug:
                    print(f"DEBUG: Using {layer_clipper} extent for '{filename}': {extent}")
            width_extent = extent.width()
            height_extent = extent.height()

            if debug:
                print(f"DEBUG: Final extent dimensions: {width_extent} x {height_extent}")
                print(f"DEBUG: Extent is empty: {extent.isEmpty()}")
                print(f"DEBUG: Extent is null: {extent.isNull()}")

            # Check for invalid extent
            if extent.isEmpty() or extent.isNull() or width_extent <= 0 or height_extent <= 0:
                print(f"⛔ ERROR: Invalid extent for '{filename}': {extent}")
                feedback.pushWarning(f"⛔ ERROR: Invalid extent for '{filename}' - skipping")
                continue
        
            if debug:
                print(f"DEBUG: Preparing map '{filename}' (type: {map_type_key})")
                print(f"DEBUG: Layer clipper extent: {layer_clipper.extent()}")
                # print(f"DEBUG: Current visible layers before exportMaps:")
                # current_visible = QgsProject.instance().layerTreeRoot().checkedLayers()
                #for layer in current_visible:
                #    print(f"  - {layer.name()}")

            try:
                processing.run("script:exportMaps", {
                    'INPUT': layer_clipper,
                    'MAPTYPE': map_types_names.index(map_type_key),
                    'COUNTRY': country_id,
                    'BACKLOG': False,
                    'DEBUG': debug
                })

                # Give a small delay to ensure layers are properly loaded and refreshed
                time.sleep(1)
                iface.mapCanvas().refresh()

                if debug:
                    print(f"DEBUG: exportMaps completed for '{filename}'")
                    print(f"DEBUG: Visible layers after exportMaps:")
                    post_visible = QgsProject.instance().layerTreeRoot().checkedLayers()
                    for layer in post_visible:
                        print(f"  - {layer.name()}")                

            except Exception as e:
                feedback.pushWarning(f"⛔ ERROR: Failed to prepare map '{filename}': {e}")
                print(f"⛔ ERROR: Failed to prepare map '{filename}': {e}")
                continue

            # special behavior for HIRF and targets
            if filename == "hirf" or filename == "targets_a" or filename == "targets_b":

                # Set zoom
                canvas = iface.mapCanvas()
                canvas.zoomScale(zoom_scale)
                canvas.refresh()
                if debug:
                    print(f"New map scale set to: 1:{canvas.scale()}")

                expr = QgsExpression('"status" = \'Active\'')
                request = QgsFeatureRequest(expr)

                for feature in layer_targets.getFeatures(request):
                    if self.cancel_requested:
                        print("Cancelling remaining renders.")
                        break

                    feat_id = feature['id'] if 'id' in feature.fields().names() else feature.id()
                    geom = feature.geometry()
                    if not geom:
                        continue

                    # Center canvas on feature
                    canvas.setExtent(geom.boundingBox())
                    canvas.refresh()

                    # Calculate adjusted extent with predefined ratio
                    extent = canvas.extent()
                    center_x = extent.center().x()
                    center_y = extent.center().y()
                    current_width = extent.width()
                    current_height = extent.height()
                    current_ratio = current_width / current_height
                    

                    if current_ratio > target_ratio:
                        # Too wide — reduce width
                        new_height = current_height
                        new_width = new_height * target_ratio
                    else:
                        # Too tall — reduce height
                        new_width = current_width
                        new_height = new_width / target_ratio

                    half_width = new_width / 2
                    half_height = new_height / 2
                    adjusted_extent = QgsRectangle(
                        center_x - half_width, center_y - half_height,
                        center_x + half_width, center_y + half_height
                    )

                    canvas.setExtent(adjusted_extent)
                    canvas.refresh()

                    canvas_crs = iface.mapCanvas().mapSettings().destinationCrs()
                    extent_crs = layer_clipper.crs() if hasattr(layer_clipper, 'crs') else QgsProject.instance().crs()

                    if debug:
                        print(f"DEBUG: Canvas CRS: {canvas_crs.authid()}")
                        print(f"DEBUG: Extent CRS: {extent_crs.authid()}")

                    self.render_map(
                        output_folder,
                        f"{filename}_{int(feat_id):02}",
                        fmt,
                        100,
                        px_per_unit,
                        revision,
                        overwrite,
                        adjusted_extent,
                        new_width,
                        new_height,
                        debug,
                        opnum
                    )
                
            else: 
                self.render_map(output_folder, filename, fmt, 100, px_per_unit, revision, overwrite, extent, width_extent, height_extent, debug, opnum)

        return {}