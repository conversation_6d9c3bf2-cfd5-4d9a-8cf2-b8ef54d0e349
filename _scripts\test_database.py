import psycopg2
from qgis.utils import iface
from datetime import date

# === 1. DB connection ===
conn = psycopg2.connect(
    dbname="jedsy",
    user="postgres",
    password="StrongSecurePassword123",  # <-- use your password
    host="localhost",
    port="5432"
)
cur = conn.cursor()

# === 2. Insert mission record ===
mission_data = {
    "code": "024",
    "title": "024_Kassel_Nordhessen",
    "rev": "3",
    "desc": "Test Test Test",
    "date": date.today()
}

cur.execute("""
    INSERT INTO missions (mission_code, title, revision, description, date)
    VALUES (%s, %s, %s, %s, %s)
    RETURNING id
""", (mission_data["code"], mission_data["title"], mission_data["rev"],
      mission_data["desc"], mission_data["date"]))
mission_id = cur.fetchone()[0]
conn.commit()

# === 3. Export current map canvas ===
export_path = f"E:/jedsy_exports/{mission_data['code']}_canvas_{mission_data['rev']}.png"

canvas = iface.mapCanvas()
image = canvas.grab()  # Grabs a snapshot of the visible canvas
image.save(export_path, "PNG")

# === 4. Insert image record ===
cur.execute("""
    INSERT INTO images (mission_id, type, path, format, revision)
    VALUES (%s, %s, %s, %s, %s)
""", (mission_id, 'overview', export_path, 'png', mission_data["rev"]))
conn.commit()

cur.close()
conn.close()

print(f"✅ Exported canvas and logged mission {mission_data['code']} Rev {mission_data['rev']}")
